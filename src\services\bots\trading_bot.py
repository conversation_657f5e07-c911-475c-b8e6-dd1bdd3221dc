# src/services/bots/trading_bot.py
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from core.interfaces import IBot, IStrategy, IBrokerage, IDatabase
from core.interfaces import BotStatus, MarketData
from core.exceptions import BotAlreadyRunningError, BotNotRunningError, StrategyError, BrokerageError


class TradingBot(IBot):
    """Main trading bot implementation"""
    
    def __init__(
        self,
        name: str,
        config: Dict[str, Any],
        strategy: IStrategy,
        brokerage: IBrokerage,
        database: IDatabase,
        logger: logging.Logger
    ):
        super().__init__(name, config)
        self.strategy = strategy
        self.brokerage = brokerage
        self.database = database
        self.logger = logger
        self.mode = config.get('mode', 'test')
        self.update_interval = config.get('update_interval', 5)  # seconds
        self._shutdown_event = asyncio.Event()
    
    async def initialize(self) -> None:
        """Initialize the bot"""
        self.logger.info(f"Initializing bot {self.name}")
        self.status = BotStatus.STARTING
        
        try:
            # Initialize components
            await self.database.connect()
            await self.brokerage.connect()
            await self.strategy.initialize()
            
            self.logger.info(f"Bot {self.name} initialized successfully")
        except Exception as e:
            self.status = BotStatus.ERROR
            self.logger.error(f"Failed to initialize bot {self.name}: {e}")
            raise
    
    async def start(self) -> None:
        """Start the bot"""
        if self.status == BotStatus.RUNNING:
            raise BotAlreadyRunningError(f"Bot {self.name} is already running")
        
        self.logger.info(f"Starting bot {self.name}")
        
        try:
            await self.initialize()
            self._task = asyncio.create_task(self.run())
            self.status = BotStatus.RUNNING
            self.logger.info(f"Bot {self.name} started successfully")
        except Exception as e:
            self.status = BotStatus.ERROR
            self.logger.error(f"Failed to start bot {self.name}: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the bot"""
        if self.status != BotStatus.RUNNING:
            raise BotNotRunningError(f"Bot {self.name} is not running")
        
        self.logger.info(f"Stopping bot {self.name}")
        self.status = BotStatus.STOPPING
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Wait for task to complete
        if self._task:
            try:
                await asyncio.wait_for(self._task, timeout=30.0)
            except asyncio.TimeoutError:
                self.logger.warning(f"Bot {self.name} did not stop gracefully, cancelling task")
                self._task.cancel()
                try:
                    await self._task
                except asyncio.CancelledError:
                    pass
        
        # Cleanup
        await self._cleanup()
        self.status = BotStatus.STOPPED
        self.logger.info(f"Bot {self.name} stopped")
    
    async def run(self) -> None:
        """Main bot execution loop"""
        self.logger.info(f"Bot {self.name} entering main loop")
        
        try:
            while not self._shutdown_event.is_set():
                try:
                    # Get symbols to monitor from strategy config
                    symbols = self.strategy.config.get('symbols', ['BTCUSDT'])
                    
                    for symbol in symbols:
                        # Get market data
                        market_data = await self.brokerage.get_market_data(symbol)
                        
                        # Analyze market and get trading decision
                        order_request = await self.strategy.analyze_market(market_data)
                        
                        if order_request:
                            self.logger.info(f"Strategy suggests order: {order_request}")
                            
                            # Execute order if in live mode
                            if self.mode == 'live':
                                try:
                                    order = await self.brokerage.place_order(order_request)
                                    self.logger.info(f"Order placed: {order}")
                                    await self.strategy.on_order_filled(order)
                                    
                                    # Store order in database
                                    await self._store_order(order)
                                    
                                except Exception as e:
                                    self.logger.error(f"Failed to place order: {e}")
                            else:
                                self.logger.info(f"Test mode: Would place order {order_request}")
                    
                    # Wait for next iteration
                    await asyncio.sleep(self.update_interval)
                    
                except Exception as e:
                    self.logger.error(f"Error in bot main loop: {e}")
                    await asyncio.sleep(5)  # Brief pause before retrying
                    
        except asyncio.CancelledError:
            self.logger.info(f"Bot {self.name} main loop cancelled")
        except Exception as e:
            self.logger.error(f"Fatal error in bot {self.name}: {e}")
            self.status = BotStatus.ERROR
        finally:
            await self._cleanup()
    
    async def _store_order(self, order) -> None:
        """Store order in database"""
        try:
            query = """
                INSERT INTO orders (id, symbol, side, order_type, quantity, price, status, created_at)
                VALUES (:id, :symbol, :side, :order_type, :quantity, :price, :status, :created_at)
            """
            params = {
                'id': order.id,
                'symbol': order.symbol,
                'side': order.side.value,
                'order_type': order.order_type.value,
                'quantity': order.quantity,
                'price': order.price,
                'status': order.status,
                'created_at': order.created_at or datetime.now()
            }
            await self.database.execute(query, params)
        except Exception as e:
            self.logger.error(f"Failed to store order: {e}")
    
    async def _cleanup(self) -> None:
        """Cleanup resources"""
        try:
            if self.strategy:
                await self.strategy.shutdown()
            if self.brokerage:
                await self.brokerage.disconnect()
            if self.database:
                await self.database.disconnect()
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get bot status information"""
        return {
            'name': self.name,
            'status': self.status.value,
            'mode': self.mode,
            'strategy': self.strategy.__class__.__name__ if self.strategy else None,
            'brokerage': self.brokerage.__class__.__name__ if self.brokerage else None,
            'is_running': self.is_running
        }