# src/core/__init__.py
"""
Core module containing interfaces, exceptions, and base classes
"""

from .interfaces import (
    BotStatus, OrderSide, OrderType, 
    OrderRequest, Order, Balance, Ticker, MarketData,
    IStrategy, IBrokerage, IBot, IDatabase
)
from .exceptions import (
    TradingBotException, ConfigurationError, BotNotFoundError,
    BotAlreadyRunningError, BotNotRunningError, StrategyError,
    BrokerageError, DatabaseError, OrderError
)
from .bot import BaseBot

__all__ = [
    'BotStatus', 'OrderSide', 'OrderType',
    'OrderRequest', 'Order', 'Balance', 'Ticker', 'MarketData',
    'IStrategy', 'IBrokerage', 'IBot', 'IDatabase',
    'TradingBotException', 'ConfigurationError', 'BotNotFoundError',
    'BotAlreadyRunningError', 'BotNotRunningError', 'StrategyError',
    'BrokerageError', 'DatabaseError', 'OrderError',
    'BaseBot'
]