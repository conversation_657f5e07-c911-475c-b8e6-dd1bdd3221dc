"""
Binance brokerage implementation for trading operations.
"""
from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio
import logging
from binance import AsyncClient, BinanceSocketManager
from binance.exceptions import BinanceAPIException, BinanceOrderException

from services.brokerages.base_brokerage import BaseBrokerage
from core.interfaces import IBrokerage, OrderRequest, Order, Balance, Ticker, MarketData, OrderSide, OrderType
from core.exceptions import BrokerageError, OrderError


class BinanceBrokerage(BaseBrokerage):
    """
    Binance exchange brokerage implementation using python-binance.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key')
        self.api_secret = config.get('api_secret')
        self.testnet = config.get('testnet', False)
        self._client: Optional[AsyncClient] = None
        self._socket_manager: Optional[BinanceSocketManager] = None
        self._connected = False
        
        if not self.api_key or not self.api_secret:
            raise BrokerageError("API key and secret are required for Binance brokerage")
    
    async def connect(self) -> None:
        """Connect to Binance API."""
        try:
            await super().connect()
            self._client = await AsyncClient.create(
                api_key=self.api_key,
                api_secret=self.api_secret,
                testnet=self.testnet
            )
            
            # Test connection
            await self._client.ping()
            self._socket_manager = BinanceSocketManager(self._client)
            self._connected = True
            
            self.logger.info(f"Connected to Binance {'testnet' if self.testnet else 'mainnet'}")
            
        except BinanceAPIException as e:
            raise BrokerageError(f"Failed to connect to Binance: {e}")
        except Exception as e:
            raise BrokerageError(f"Unexpected error connecting to Binance: {e}")
    
    async def disconnect(self) -> None:
        """Disconnect from Binance API."""
        try:
            if self._socket_manager:
                await self._socket_manager.close()
                self._socket_manager = None
            
            if self._client:
                await self._client.close_connection()
                self._client = None
            
            self._connected = False
            await super().disconnect()
            self.logger.info("Disconnected from Binance")
            
        except Exception as e:
            self.logger.error(f"Error disconnecting from Binance: {e}")
    
    async def get_account_balance(self) -> List[Balance]:
        """Get account balance from Binance."""
        self._validate_connection()
        
        try:
            account_info = await self._client.get_account()
            balances = []
            
            for balance_data in account_info['balances']:
                free_amount = float(balance_data['free'])
                locked_amount = float(balance_data['locked'])
                
                # Only include assets with non-zero balances
                if free_amount > 0 or locked_amount > 0:
                    balances.append(Balance(
                        asset=balance_data['asset'],
                        free=free_amount,
                        locked=locked_amount,
                        total=free_amount + locked_amount
                    ))
            
            return balances
            
        except BinanceAPIException as e:
            raise BrokerageError(f"Failed to get account balance: {e}")
    
    async def get_ticker(self, symbol: str) -> Ticker:
        """Get ticker data for a symbol."""
        self._validate_connection()
        
        try:
            ticker_data = await self._client.get_symbol_ticker(symbol=symbol.upper())
            
            return Ticker(
                symbol=symbol.upper(),
                price=float(ticker_data['price']),
                timestamp=datetime.now()
            )
            
        except BinanceAPIException as e:
            raise BrokerageError(f"Failed to get ticker for {symbol}: {e}")
    
    async def get_market_data(self, symbol: str) -> MarketData:
        """Get detailed market data for a symbol."""
        self._validate_connection()
        
        try:
            # Get order book ticker for bid/ask
            ticker_24hr = await self._client.get_ticker(symbol=symbol.upper())
            order_book = await self._client.get_orderbook_ticker(symbol=symbol.upper())
            
            return MarketData(
                symbol=symbol.upper(),
                bid=float(order_book['bidPrice']),
                ask=float(order_book['askPrice']),
                last_price=float(ticker_24hr['lastPrice']),
                volume=float(ticker_24hr['volume']),
                timestamp=datetime.now()
            )
            
        except BinanceAPIException as e:
            raise BrokerageError(f"Failed to get market data for {symbol}: {e}")
    
    async def place_order(self, order_request: OrderRequest) -> Order:
        """Place an order on Binance."""
        self._validate_connection()
        
        try:
            # Map internal order types to Binance order types
            binance_side = self._map_order_side(order_request.side)
            binance_type = self._map_order_type(order_request.order_type)
            
            order_params = {
                'symbol': order_request.symbol.upper(),
                'side': binance_side,
                'type': binance_type,
                'quantity': order_request.quantity,
                'timeInForce': order_request.time_in_force or 'GTC'
            }
            
            # Add price for limit orders
            if order_request.order_type == OrderType.LIMIT and order_request.price:
                order_params['price'] = order_request.price
            
            # Add stop price for stop orders
            if order_request.order_type in [OrderType.STOP_LOSS, OrderType.TAKE_PROFIT] and order_request.stop_price:
                order_params['stopPrice'] = order_request.stop_price
            
            # Add client order ID if provided
            if order_request.client_order_id:
                order_params['newClientOrderId'] = order_request.client_order_id
            
            # Place the order
            result = await self._client.create_order(**order_params)
            
            return self._map_binance_order_to_order(result)
            
        except BinanceOrderException as e:
            raise OrderError(f"Failed to place order: {e}")
        except BinanceAPIException as e:
            raise BrokerageError(f"API error placing order: {e}")
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an existing order."""
        self._validate_connection()
        
        try:
            await self._client.cancel_order(
                symbol=symbol.upper(),
                orderId=int(order_id)
            )
            return True
            
        except BinanceAPIException as e:
            self.logger.error(f"Failed to cancel order {order_id}: {e}")
            return False
    
    async def get_order(self, order_id: str, symbol: str) -> Optional[Order]:
        """Get order status by ID."""
        self._validate_connection()
        
        try:
            result = await self._client.get_order(
                symbol=symbol.upper(),
                orderId=int(order_id)
            )
            
            return self._map_binance_order_to_order(result)
            
        except BinanceAPIException as e:
            self.logger.error(f"Failed to get order {order_id}: {e}")
            return None
    
    async def get_open_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """Get all open orders."""
        self._validate_connection()
        
        try:
            params = {}
            if symbol:
                params['symbol'] = symbol.upper()
            
            results = await self._client.get_open_orders(**params)
            
            orders = []
            for order_data in results:
                orders.append(self._map_binance_order_to_order(order_data))
            
            return orders
            
        except BinanceAPIException as e:
            raise BrokerageError(f"Failed to get open orders: {e}")
    
    def _map_order_side(self, side: OrderSide) -> str:
        """Map internal OrderSide to Binance side."""
        mapping = {
            OrderSide.BUY: 'BUY',
            OrderSide.SELL: 'SELL'
        }
        return mapping[side]
    
    def _map_order_type(self, order_type: OrderType) -> str:
        """Map internal OrderType to Binance order type."""
        mapping = {
            OrderType.MARKET: 'MARKET',
            OrderType.LIMIT: 'LIMIT',
            OrderType.STOP_LOSS: 'STOP_LOSS_LIMIT',
            OrderType.TAKE_PROFIT: 'TAKE_PROFIT_LIMIT'
        }
        return mapping[order_type]
    
    def _map_binance_order_to_order(self, binance_order: Dict[str, Any]) -> Order:
        """Map Binance order response to internal Order object."""
        return Order(
            id=str(binance_order['orderId']),
            symbol=binance_order['symbol'],
            side=OrderSide.BUY if binance_order['side'] == 'BUY' else OrderSide.SELL,
            order_type=self._map_binance_type_to_order_type(binance_order['type']),
            quantity=float(binance_order['origQty']),
            price=float(binance_order['price']) if binance_order['price'] != '0.00000000' else None,
            status=binance_order['status'],
            filled_quantity=float(binance_order['executedQty']),
            created_at=datetime.fromtimestamp(binance_order['time'] / 1000) if 'time' in binance_order else None,
            updated_at=datetime.fromtimestamp(binance_order['updateTime'] / 1000) if 'updateTime' in binance_order else None
        )
    
    def _map_binance_type_to_order_type(self, binance_type: str) -> OrderType:
        """Map Binance order type to internal OrderType."""
        mapping = {
            'MARKET': OrderType.MARKET,
            'LIMIT': OrderType.LIMIT,
            'STOP_LOSS_LIMIT': OrderType.STOP_LOSS,
            'TAKE_PROFIT_LIMIT': OrderType.TAKE_PROFIT
        }
        return mapping.get(binance_type, OrderType.LIMIT)
    
    def _validate_connection(self) -> None:
        """Validate that the brokerage is connected."""
        if not self._connected or not self._client:
            raise BrokerageError("Not connected to Binance. Call connect() first.")