# src/api/app.py
from fastapi import FastAPI
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
from manager.bot_manager import <PERSON>t<PERSON>anager
from manager.config_loader import ConfigLoader
from core.exceptions import (
    BotNotFoundError, BotAlreadyRunningError, BotNotRunningError,
    ConfigurationError
)
from .endpoints import router

# Global bot manager instance
bot_manager: BotManager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    global bot_manager

    # Startup
    try:
        # Load configuration
        config = ConfigLoader.load_from_file("config/config.json")
        ConfigLoader.validate_config(config)

        # Initialize bot manager
        bot_manager = BotManager(config)
        await bot_manager.initialize()

        logging.info("FastAPI application started successfully")
        yield

    except Exception as e:
        logging.error(f"Failed to start application: {e}")
        raise
    finally:
        # Shutdown
        if bot_manager:
            await bot_manager.shutdown()
        logging.info("FastAPI application shutdown completed")


# Create FastAPI app
app = FastAPI(
    title="Trading Bot Manager",
    description="API for managing trading bots",
    version="1.0.0",
    lifespan=lifespan
)

app.include_router(router)


# Exception handlers
@app.exception_handler(BotNotFoundError)
async def bot_not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"error": "Bot not found", "detail": str(exc)}
    )


@app.exception_handler(BotAlreadyRunningError)
async def bot_already_running_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"error": "Bot already running", "detail": str(exc)}
    )


@app.exception_handler(BotNotRunningError)
async def bot_not_running_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"error": "Bot not running", "detail": str(exc)}
    )


@app.exception_handler(ConfigurationError)
async def configuration_error_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"error": "Configuration error", "detail": str(exc)}
    )
