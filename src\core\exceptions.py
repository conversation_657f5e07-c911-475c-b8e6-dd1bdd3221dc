# src/core/exceptions.py
"""Custom exceptions for the trading bot manager"""


class TradingBotException(Exception):
    """Base exception for trading bot errors"""
    pass


class ConfigurationError(TradingBotException):
    """Raised when there's an error in configuration"""
    pass


class BotNotFoundError(TradingBotException):
    """Raised when a bot is not found"""
    pass


class BotAlreadyRunningError(TradingBotException):
    """Raised when trying to start an already running bot"""
    pass


class BotNotRunningError(TradingBotException):
    """Raised when trying to stop a bot that's not running"""
    pass


class StrategyError(TradingBotException):
    """Raised when there's an error in strategy execution"""
    pass


class BrokerageError(TradingBotException):
    """Raised when there's an error with brokerage operations"""
    pass


class DatabaseError(TradingBotException):
    """Raised when there's a database error"""
    pass


class OrderError(TradingBotException):
    """Raised when there's an error with order operations"""
    pass

# Custom exceptions
class BrokerageError(Exception):
    """Base brokerage error"""
    pass


class OrderError(BrokerageError):
    """Order-related error"""
    pass


class ConnectionError(BrokerageError):
    """Connection-related error"""
    pass


class StrategyError(Exception):
    """Strategy-related error"""
    pass


class BotError(Exception):
    """Bot-related error"""
    pass


class RiskManagementError(Exception):
    """Risk management error"""
    pass