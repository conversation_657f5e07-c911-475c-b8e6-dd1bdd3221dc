# src/manager/bot_manager.py
import asyncio
import importlib
from typing import Dict, Any, List, Optional
from manager.config_loader import Config, BotConfig
from core.interfaces import IBot, IStrategy, IBrokerage, IDatabase, BotStatus
from core.exceptions import (
    BotNotFoundError, BotAlreadyRunningError, BotNotRunningError,
    ConfigurationError, StrategyError, BrokerageError
)
from services.database.factory import DatabaseFactory
from services.logging.factory import LoggingFactory


class BotManager:
    """Manager for orchestrating multiple trading bots"""

    def __init__(self, config: Config):
        self.config = config
        self.bots: Dict[str, IBot] = {}
        self.database = None
        self.logger = None
        self._initialize_manager()

    def _initialize_manager(self) -> None:
        """Initialize the manager's own resources"""
        # Setup logging
        self.logger = LoggingFactory.create_logger(
            "BotManager",
            self.config.manager.logging.dict()
        )

        # Setup database
        self.database = DatabaseFactory.create_database(
            self.config.manager.database.dict()
        )

        self.logger.info("Bot Manager initialized")

    async def initialize(self) -> None:
        """Initialize the manager and all configured bots"""
        self.logger.info("Initializing Bot Manager")

        try:
            # Connect manager database
            await self.database.connect()

            # Initialize all bots
            for bot_config in self.config.manager.bots:
                await self._create_bot(bot_config)

            self.logger.info(
                f"Bot Manager initialized with {len(self.bots)} bots")

        except Exception as e:
            self.logger.error(f"Failed to initialize Bot Manager: {e}")
            raise

    async def _create_bot(self, bot_config: BotConfig) -> None:
        """Create and configure a bot"""
        try:
            self.logger.info(f"Creating bot: {bot_config.name}")

            # Create bot logger
            bot_logger = LoggingFactory.create_logger(
                f"bot.{bot_config.name}",
                bot_config.logging.dict()
            )

            # Create bot database
            bot_database = DatabaseFactory.create_database(
                bot_config.database.dict())

            # Create strategy
            strategy = await self._create_strategy(bot_config.strategy)

            # Create brokerage
            brokerage = await self._create_brokerage(bot_config.brokerage)

            # Create bot instance
            bot_class = self._load_class(
                bot_config.module, bot_config.class_name)
            bot = bot_class(
                name=bot_config.name,
                config=bot_config.dict(),
                strategy=strategy,
                brokerage=brokerage,
                database=bot_database,
                logger=bot_logger
            )

            self.bots[bot_config.name] = bot
            self.logger.info(f"Bot {bot_config.name} created successfully")

        except Exception as e:
            self.logger.error(f"Failed to create bot {bot_config.name}: {e}")
            raise

    async def _create_strategy(self, strategy_config) -> IStrategy:
        """Create a strategy instance"""
        try:
            strategy_class = self._load_class(
                strategy_config.module,
                strategy_config.class_name
            )
            return strategy_class(strategy_config.params)
        except Exception as e:
            raise StrategyError(f"Failed to create strategy: {e}")

    async def _create_brokerage(self, brokerage_config) -> IBrokerage:
        """Create a brokerage instance"""
        try:
            brokerage_class = self._load_class(
                brokerage_config.module,
                brokerage_config.class_name
            )
            return brokerage_class(brokerage_config.params)
        except Exception as e:
            raise BrokerageError(f"Failed to create brokerage: {e}")

    def _load_class(self, module_name: str, class_name: str):
        """Dynamically load a class from a module"""
        try:
            module = importlib.import_module(module_name)
            return getattr(module, class_name)
        except ImportError as e:
            raise ConfigurationError(f"Module {module_name} not found: {e}")
        except AttributeError as e:
            raise ConfigurationError(
                f"Class {class_name} not found in {module_name}: {e}")

    async def start_bot(self, bot_name: str) -> None:
        """Start a specific bot"""
        bot = self._get_bot(bot_name)

        if bot.status == BotStatus.RUNNING:
            raise BotAlreadyRunningError(f"Bot {bot_name} is already running")

        self.logger.info(f"Starting bot: {bot_name}")
        await bot.start()
        self.logger.info(f"Bot {bot_name} started successfully")

    async def stop_bot(self, bot_name: str) -> None:
        """Stop a specific bot"""
        bot = self._get_bot(bot_name)

        if bot.status != BotStatus.RUNNING:
            raise BotNotRunningError(f"Bot {bot_name} is not running")

        self.logger.info(f"Stopping bot: {bot_name}")
        await bot.stop()
        self.logger.info(f"Bot {bot_name} stopped successfully")

    async def restart_bot(self, bot_name: str) -> None:
        """Restart a specific bot"""
        bot = self._get_bot(bot_name)

        if bot.status == BotStatus.RUNNING:
            await self.stop_bot(bot_name)

        await self.start_bot(bot_name)

    async def start_all_bots(self) -> None:
        """Start all bots"""
        self.logger.info("Starting all bots")

        tasks = []
        for bot_name, bot in self.bots.items():
            if bot.status != BotStatus.RUNNING:
                tasks.append(self.start_bot(bot_name))

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

        self.logger.info("All bots start process completed")

    async def stop_all_bots(self) -> None:
        """Stop all bots"""
        self.logger.info("Stopping all bots")

        tasks = []
        for bot_name, bot in self.bots.items():
            if bot.status == BotStatus.RUNNING:
                tasks.append(self.stop_bot(bot_name))

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

        self.logger.info("All bots stopped")

    def get_bot_status(self, bot_name: str) -> Dict[str, Any]:
        """Get status of a specific bot"""
        bot = self._get_bot(bot_name)
        return bot.get_status()

    def get_all_bots_status(self) -> List[Dict[str, Any]]:
        """Get status of all bots"""
        return [bot.get_status() for bot in self.bots.values()]

    def list_bots(self) -> List[str]:
        """List all bot names"""
        return list(self.bots.keys())

    def _get_bot(self, bot_name: str) -> IBot:
        """Get bot by name"""
        if bot_name not in self.bots:
            raise BotNotFoundError(f"Bot {bot_name} not found")
        return self.bots[bot_name]

    async def shutdown(self) -> None:
        """Shutdown the manager and all bots"""
        self.logger.info("Shutting down Bot Manager")

        try:
            # Stop all bots
            await self.stop_all_bots()

            # Disconnect manager database
            if self.database:
                await self.database.disconnect()

            self.logger.info("Bot Manager shutdown completed")

        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

    async def reload_config(self, config: Config) -> None:
        """Reload configuration (stops all bots and recreates them)"""
        self.logger.info("Reloading configuration")

        # Stop all current bots
        await self.stop_all_bots()

        # Clear current bots
        self.bots.clear()

        # Update config
        self.config = config

        # Reinitialize with new config
        await self.initialize()

        self.logger.info("Configuration reloaded successfully")
