# src/main.py
from manager.config_loader import Config<PERSON>oader
from api.app import app
import asyncio
import uvicorn
import logging
import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent))


def setup_logging():
    """Setup basic logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/main.log')
        ]
    )


def create_directories():
    """Create necessary directories"""
    directories = ['logs', 'config']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


def create_sample_config():
    """Create sample configuration if it doesn't exist"""
    config_path = "config/config.json"
    if not os.path.exists(config_path):
        print(f"Creating sample configuration at {config_path}")
        ConfigLoader.create_sample_config(config_path)
        print("Please edit the configuration file before running the application")
        return False
    return True


async def main():
    """Main application entry point"""
    # Setup
    create_directories()
    setup_logging()

    # Check configuration
    if not create_sample_config():
        return

    # Start the application
    config = uvicorn.Config(
        app="api.app:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )

    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    asyncio.run(main())
